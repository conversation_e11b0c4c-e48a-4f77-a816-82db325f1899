<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Chat with HAMMAD Bhai 🤖</title>

    <!-- Creator & Copyright Information -->
    <meta name="author" content="M<PERSON><PERSON><PERSON><PERSON> HAMMAD ZUBAIR" />
    <meta name="creator" content="MUHAMMAD HAMMAD ZUBAIR" />
    <meta name="developer" content="MUHAMMAD HAMMAD ZUBAIR" />
    <meta
      name="description"
      content="HAMMAD BHAI AI Assistant - Exclusively created, designed and developed by MUHAMMAD HAMMAD ZUBAIR."
    />
    <meta
      name="keywords"
      content="HAMMAD BHAI, MU<PERSON><PERSON><PERSON> HAMMAD ZUBAIR, AI Assistant, Chatbot, Gemini AI"
    />
    <meta
      name="copyright"
      content="© 2025 MUHAMMAD HAMMAD ZUBAIR. All rights reserved."
    />

    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />

    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}?v=2025"
    />
  </head>
  <body>
    <header class="main-header">
      <h1>
        <i class="fas fa-robot"></i> Chat with
        <span class="highlight">HAMMAD Bhai</span>
      </h1>
      <p>Your friendly AI assistant at your service! 🤖✨</p>
      <p style="font-size: 0.9rem; color: #64ffda; margin-top: 0.5rem">
        🌟 Enhanced thinking • Multi-language support • Unlimited usage • 100%
        Free
      </p>
      <p
        style="
          font-size: 0.8rem;
          color: #ff6363;
          margin-top: 0.3rem;
          font-weight: 600;
        "
      >
        🔥 Proudly Created & Developed by
        <strong>MUHAMMAD HAMMAD ZUBAIR</strong> 🔥
      </p>

      <!-- Model Selector Section -->
      <div class="model-selector-section">
        <p
          style="
            color: #64ffda;
            font-size: 0.9rem;
            margin-bottom: 0.8rem;
            margin-top: 1rem;
          "
        >
          <i class="fas fa-brain"></i> <strong>Choose Your AI Model:</strong>
        </p>
        <button
          id="model-selector-btn"
          class="model-selector-main-btn"
          title="Click to Switch AI Model"
          onclick="openModelModal()"
        >
          <div class="model-btn-content">
            <i class="fas fa-robot"></i>
            <div class="model-info">
              <span class="current-model-label">Current Model:</span>
              <span id="current-model-display" class="current-model-name"
                >Loading...</span
              >
            </div>
            <i class="fas fa-chevron-down model-arrow"></i>
          </div>
        </button>
      </div>
    </header>

    <!-- Model Selector Modal -->
    <div id="model-modal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-brain"></i> Choose AI Model</h3>
          <button class="close-btn" onclick="closeModelModal()">&times;</button>
        </div>
        <div class="modal-body">
          <p style="color: #64ffda; margin-bottom: 15px">
            <i class="fas fa-info-circle"></i> Select the AI model that best
            fits your needs:
          </p>
          <div id="model-list" class="model-list">
            <!-- Models will be loaded here -->
          </div>
        </div>
      </div>
    </div>

    <main class="chat-container">
      <div class="chat-messages" id="chat-messages">
        <div class="message bot-message" data-message-id="welcome_msg">
          <div class="message-content">
            ✨ Hey there, I'm <strong>HAMMAD BHAI</strong> 🤖 — your smart chat
            buddy, crafted with 💙 by <strong>MUHAMMAD HAMMAD ZUBAIR</strong>.
            Let’s brighten your day 🌟 and tackle anything, together! 💼💬 Type
            below ⌨️ and let’s begin the fun! 🚀

            <div class="message-timestamp" id="welcome-timestamp"></div>
          </div>
          <div class="message-actions">
            <button
              class="action-btn copy-btn"
              title="Copy message"
              onclick="copyMessage('welcome_msg')"
            >
              <i class="fas fa-copy"></i>
            </button>
            <button
              class="action-btn regenerate-btn"
              title="Regenerate response"
              onclick="regenerateMessage('welcome_msg')"
            >
              <i class="fas fa-redo"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Typing indicator will be added dynamically when needed -->

      <form id="chat-form" class="chat-input-container" autocomplete="off">
        <textarea
          id="user-input"
          placeholder="Message HAMMAD BHAI..."
          rows="1"
          required
        ></textarea>
        <button type="submit" id="send-btn" aria-label="Send Message">
          <i class="fas fa-paper-plane"></i>
        </button>
        <button
          type="button"
          id="stop-btn"
          aria-label="Stop Generation"
          style="display: none"
        >
          <i class="fas fa-stop"></i>
        </button>
      </form>

      <button id="reset-btn" title="Reset Chat">
        <i class="fas fa-redo-alt"></i> Reset Chat
      </button>
    </main>

    <footer class="main-footer">
      🚀 <strong>HAMMAD BHAI AI Assistant</strong> - Exclusively Created,
      Designed & Developed by <strong>MUHAMMAD HAMMAD ZUBAIR</strong> 🚀<br />
      📅 Chat-Bot Born on: <strong>20/May/2025</strong> | 🏆 Original Creator:
      <strong>MUHAMMAD HAMMAD ZUBAIR</strong><br />
      🌟 A true testament that passion and perseverance turn dreams into
      reality. 💪🔥✨<br />
      ⚡ <strong>100% Original Work by MUHAMMAD HAMMAD ZUBAIR</strong> ⚡
    </footer>

    <script src="{{ url_for('static', filename='js/script.js') }}?v=2025"></script>
    <script>
      // Set welcome message timestamp
      document.addEventListener("DOMContentLoaded", function () {
        const welcomeTimestamp = document.getElementById("welcome-timestamp");
        if (welcomeTimestamp) {
          const timestamp = new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });
          welcomeTimestamp.textContent = timestamp;
        }
      });
    </script>
  </body>
</html>
