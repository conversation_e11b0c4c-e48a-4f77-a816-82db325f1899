/* RESET */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
  font-family: "Poppins", sans-serif;
  color: #e0e6f0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  line-height: 1.6;
}

/* HEADER */
.main-header {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 0 15px #64ffdaaa;
  animation: pulseGlow 3s ease-in-out infinite;
}

.main-header h1 {
  font-weight: 700;
  font-size: 2.5rem;
  color: #64ffda;
  text-shadow: 0 0 8px #64ffda;
  margin-bottom: 0.5rem;
}

.main-header .highlight {
  color: #ff6363;
  text-shadow: 0 0 12px #ff6363;
}

.main-header p {
  font-size: 1.1rem;
  margin-top: 0.5rem;
  color: #c0d6df;
  font-style: italic;
  text-shadow: 0 0 6px #00000066;
}

/* CHAT CONTAINER */
.chat-container {
  max-width: 720px;
  width: 95%;
  margin: 1.5rem auto;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 15px;
  box-shadow: 0 0 40px #64ffda44;
  display: flex;
  flex-direction: column;
  height: 70vh;
  overflow: hidden;
  animation: slideInUp 0.8s ease forwards;
  position: relative;
}

/* CHAT MESSAGES */
.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  scrollbar-width: thin;
  scrollbar-color: #64ffda66 transparent;
  background: transparent;
}

.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #64ffda88;
  border-radius: 10px;
}

.message {
  max-width: 85%;
  margin-bottom: 1rem;
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(15px);
  word-wrap: break-word;
  position: relative;
  display: flex;
  flex-direction: column;
}

.message:hover .message-actions {
  opacity: 1;
  visibility: visible;
}

.message-content {
  padding: 12px 20px;
  border-radius: 25px;
  box-shadow: 0 4px 12px #00000044;
  font-size: 1rem;
  line-height: 1.5;
  user-select: text;
  position: relative;
}

.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.6;
  margin-top: 8px;
  text-align: right;
  font-weight: 400;
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  align-self: flex-end;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #e0e6f0;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.copy-btn:hover {
  background: rgba(100, 255, 218, 0.2);
  color: #64ffda;
}

.edit-btn:hover {
  background: rgba(255, 195, 113, 0.2);
  color: #ffc371;
}

.regenerate-btn:hover {
  background: rgba(255, 99, 99, 0.2);
  color: #ff6363;
}

.stop-btn {
  background: rgba(255, 99, 99, 0.3) !important;
  color: #ff6363 !important;
  animation: pulse 1.5s infinite;
}

.stop-btn:hover {
  background: rgba(255, 99, 99, 0.5) !important;
}

/* BOT MESSAGE */
.bot-message {
  align-self: flex-start;
  text-align: left;
}

.bot-message .message-content {
  background: linear-gradient(135deg, #0fffd6, #00c9ff);
  color: #002b36;
  font-weight: 600;
  border-bottom-left-radius: 4px;
  animation: botMessageGlow 2.5s ease-in-out infinite;
  box-shadow: 0 0 12px #00fffdbb;
}

/* USER MESSAGE */
.user-message {
  align-self: flex-end;
  text-align: right;
}

.user-message .message-content {
  background: linear-gradient(135deg, #ff5f6d, #ffc371);
  color: #2f1b17;
  font-weight: 600;
  border-bottom-right-radius: 4px;
  box-shadow: 0 0 12px #ff7f50cc;
  animation: userMessageGlow 3s ease-in-out infinite;
}

.user-message .message-actions {
  align-self: flex-start;
}

/* CODE BLOCKS */
.code-block {
  background: #1a202c;
  border-radius: 12px;
  margin: 16px 0;
  overflow: hidden;
  border: 1px solid #2d3748;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.code-header {
  background: #2d3748;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #4a5568;
}

.code-language {
  font-size: 0.8rem;
  font-weight: 600;
  color: #a0aec0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-copy-btn {
  background: #4a5568;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  color: #e2e8f0;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.code-copy-btn:hover {
  background: #718096;
  transform: translateY(-1px);
}

.code-block pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
  background: transparent;
}

.code-block code {
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  font-size: 0.85rem;
  line-height: 1.6;
  color: #e2e8f0;
  background: transparent;
}

/* CLEAN SYNTAX HIGHLIGHTING FOR PROGRAMMING */
.code-block .keyword {
  color: #ff6b9d !important;
  font-weight: 600;
}

.code-block .string {
  color: #4ecdc4 !important;
}

.code-block .comment {
  color: #95a5a6 !important;
  font-style: italic;
  opacity: 0.8;
}

.code-block .number {
  color: #a29bfe !important;
}

.code-block .function {
  color: #feca57 !important;
  font-weight: 500;
}

.code-block .operator {
  color: #ff7675 !important;
}

/* Ensure no nested highlighting conflicts */
.code-block span span {
  color: inherit !important;
  font-weight: inherit !important;
  font-style: inherit !important;
}

.inline-code {
  background: #edf2f7;
  color: #d69e2e;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  font-size: 0.85em;
  border: 1px solid #e2e8f0;
  font-weight: 500;
}

/* TYPING INDICATOR */
.typing-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem 0.8rem;
  gap: 5px;
}

.typing-indicator span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #64ffda;
  border-radius: 50%;
  animation: typingBounce 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}
.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* INPUT AREA */
.chat-input-container {
  display: flex;
  padding: 1rem;
  gap: 1rem;
  background: rgba(0, 0, 0, 0.15);
  border-top: 1px solid #64ffda44;
  flex-wrap: nowrap;
  align-items: center;
}

.chat-input-container textarea {
  flex-grow: 1;
  border-radius: 25px;
  border: none;
  padding: 12px 20px;
  font-size: 1rem;
  font-family: "Poppins", sans-serif;
  resize: none;
  max-height: 120px;
  box-shadow: inset 0 0 10px #64ffda55;
  color: #0d0d0d;
  background: #e0f7f9;
  transition: box-shadow 0.3s ease;
}

.chat-input-container textarea:focus {
  outline: none;
  box-shadow: inset 0 0 15px #00fffdbb;
}

#send-btn,
#stop-btn {
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  cursor: pointer;
  font-size: 1.4rem;
  transition: transform 0.25s ease, box-shadow 0.3s ease;
}

#send-btn {
  background: #64ffda;
  color: #002b36;
  box-shadow: 0 0 12px #64ffdaaa;
}

#send-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 0 25px #00fffdbb;
}

#stop-btn {
  background: #ff6363;
  color: #ffffff;
  box-shadow: 0 0 12px #ff6363aa;
  display: none;
  animation: pulse 1.5s infinite;
}

#stop-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 0 25px #ff6363bb;
}

/* RESET BUTTON */
#reset-btn {
  width: 95%;
  max-width: 720px;
  margin: 1rem auto 2rem;
  display: block;
  background: #ff6363;
  border: none;
  padding: 12px 20px;
  color: #fff;
  font-weight: 600;
  font-size: 1.1rem;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 0 20px #ff6363aa;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  user-select: none;
}

#reset-btn:hover {
  background: #ff4949;
  box-shadow: 0 0 30px #ff4949cc;
}

/* FOOTER */
.main-footer {
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
  color: #99aabb;
  background: rgba(255, 255, 255, 0.04);
  box-shadow: inset 0 0 10px #00000022;
}

/* ANIMATIONS */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes typingBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}
@keyframes botMessageGlow {
  0%,
  100% {
    box-shadow: 0 0 12px #00fffdbb;
  }
  50% {
    box-shadow: 0 0 25px #00fffddd;
  }
}
@keyframes userMessageGlow {
  0%,
  100% {
    box-shadow: 0 0 12px #ff7f50cc;
  }
  50% {
    box-shadow: 0 0 25px #ff7f50ee;
  }
}
@keyframes pulseGlow {
  0%,
  100% {
    text-shadow: 0 0 8px #64ffda;
  }
  50% {
    text-shadow: 0 0 20px #00fffdbb;
  }
}
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* MODEL SELECTOR STYLES */
.model-selector-section {
  margin-top: 1.5rem;
  text-align: center;
}

.model-selector-main-btn {
  background: linear-gradient(
    135deg,
    rgba(100, 255, 218, 0.1) 0%,
    rgba(100, 255, 218, 0.05) 100%
  );
  border: 2px solid rgba(100, 255, 218, 0.3);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  display: block;
}

.model-selector-main-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(100, 255, 218, 0.2) 0%,
    rgba(100, 255, 218, 0.1) 100%
  );
  border-color: rgba(100, 255, 218, 0.6);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(100, 255, 218, 0.3);
}

.model-btn-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.model-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.current-model-label {
  font-size: 0.8rem;
  color: #b0b0b0;
  margin-bottom: 0.2rem;
}

.current-model-name {
  font-size: 1.1rem;
  color: #64ffda;
  font-weight: 700;
}

.model-arrow {
  font-size: 1rem;
  color: #64ffda;
  transition: transform 0.3s ease;
}

.model-selector-main-btn:hover .model-arrow {
  transform: rotate(180deg);
}

@media (max-width: 768px) {
  .model-selector-main-btn {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }

  .current-model-name {
    font-size: 1rem;
  }

  /* Modal Responsive Styles */
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 1rem;
    max-height: 90vh;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-header h3 {
    font-size: 1.1rem;
  }

  .modal-body {
    padding: 1rem;
    max-height: 70vh;
  }

  .model-item {
    padding: 1rem;
  }

  .model-name {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .model-description {
    font-size: 0.85rem;
    margin-bottom: 0.6rem;
    line-height: 1.3;
  }

  .model-stats {
    flex-wrap: wrap;
    gap: 0.5rem;
    font-size: 0.75rem;
  }

  .model-stat {
    gap: 0.2rem;
  }

  .model-item.recommended::before {
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
    top: -6px;
    right: 8px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 0.5rem;
    max-height: 95vh;
  }

  .modal-header {
    padding: 0.8rem;
  }

  .modal-header h3 {
    font-size: 1rem;
  }

  .modal-body {
    padding: 0.8rem;
    max-height: 75vh;
  }

  .model-item {
    padding: 0.8rem;
  }

  .model-name {
    font-size: 0.95rem;
    margin-bottom: 0.3rem;
  }

  .model-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.2;
  }

  .model-stats {
    gap: 0.4rem;
    font-size: 0.7rem;
  }

  .model-stat {
    gap: 0.15rem;
  }

  .model-item.recommended::before {
    font-size: 0.55rem;
    padding: 0.1rem 0.3rem;
    top: -5px;
    right: 5px;
  }

  .close-btn {
    font-size: 1.3rem;
    padding: 0.3rem;
  }
}

/* Extra small screens and landscape phones */
@media (max-width: 360px) {
  .modal-content {
    width: 100%;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }

  .modal-header {
    padding: 0.6rem;
  }

  .modal-header h3 {
    font-size: 0.9rem;
  }

  .modal-body {
    padding: 0.6rem;
    max-height: 80vh;
  }

  .model-item {
    padding: 0.6rem;
  }

  .model-name {
    font-size: 0.9rem;
  }

  .model-description {
    font-size: 0.75rem;
  }

  .model-stats {
    font-size: 0.65rem;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 600px) and (orientation: landscape) {
  .modal-content {
    max-height: 95vh;
  }

  .modal-body {
    max-height: 60vh;
  }

  .modal-header {
    padding: 0.8rem;
  }

  .modal-body {
    padding: 0.8rem;
  }

  .model-item {
    padding: 0.8rem;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 15px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.3s ease;
}

.modal-header {
  background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.model-list {
  display: grid;
  gap: 1rem;
}

.model-item {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.model-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #64ffda;
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(100, 255, 218, 0.2);
}

.model-item.current {
  border-color: #64ffda;
  background: rgba(100, 255, 218, 0.1);
}

.model-item.recommended::before {
  content: "⭐ RECOMMENDED";
  position: absolute;
  top: -8px;
  right: 10px;
  background: #ff6363;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.model-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #64ffda;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.model-description {
  color: #b0b0b0;
  font-size: 0.9rem;
  margin-bottom: 0.8rem;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.model-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  flex-wrap: wrap;
}

.model-stat {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #888;
  white-space: nowrap;
}

.model-stat strong {
  color: #64ffda;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
